import type { <PERSON>ada<PERSON>, Viewport } from 'next'
import { Poppin<PERSON> } from 'next/font/google'
import './globals.css'
import { Analytics } from '@vercel/analytics/react'
import { SpeedInsights } from '@vercel/speed-insights/next'

const poppins = Poppins({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
  variable: '--font-poppins',
  display: 'swap',
})

export const metadata: Metadata = {
  title: 'EverKind',
  description: 'AI-Powered Wellness For Your Mind',
  generator: 'v0.dev',
  metadataBase: new URL('https://www.everkind.com'),
  alternates: {
    canonical: '/',
    types: {
      'application/xml': 'https://www.everkind.com/sitemap.xml',
    }
  },
  icons: {
    icon: '/logo.png',
  },
  manifest: '/manifest.webmanifest',
  openGraph: {
    title: 'EverKind - AI-Powered Wellness For Your Mind',
    description: 'Ever<PERSON><PERSON> combines cutting-edge AI technology with evidence-based wellness practices to help you achieve balance, clarity, and personal growth.',
    url: 'https://www.everkind.com',
    siteName: 'EverKind',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'EverKind AI Wellness Platform',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'EverKind - AI-Powered Wellness For Your Mind',
    description: 'EverKind combines cutting-edge AI technology with evidence-based wellness practices to help you achieve balance, clarity, and personal growth.',
    images: ['/og-image.png'],
    creator: '@everkind',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export const viewport: Viewport = {
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: 'white' },
    { media: '(prefers-color-scheme: dark)', color: 'black' },
  ],
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body className={`${poppins.variable} font-sans antialiased`}>
        {children}
        <Analytics />
        <SpeedInsights />
      </body>
    </html>
  )
}
