"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import Image from "next/image"
import Footer from "@/components/footer"

export default function Home() {
  const [email, setEmail] = useState("")
  const [subscriptionAgreed, setSubscriptionAgreed] = useState(false)

  const faqs = [
    {
      question: "What is Ever<PERSON><PERSON> ?",
      answer: "<PERSON><PERSON><PERSON> combines cutting-edge AI technology with evidence-based wellness practices to help you achieve balance, clarity, and personal growth."
    },
    {
      question: "Can I trust <PERSON><PERSON><PERSON>?",
      answer: "EverKind operates with the highest commitment to trust and privacy. All user data is encrypted and handled in compliance, and no data is ever sold or shared without explicit consent. We understand that trust is a prerequisite for healing—and EverKind exists to protect that trust in every session, journal entry, and conversation."
    },
    {
      question: "What makes <PERSON><PERSON><PERSON> different?",
      answer: "Ever<PERSON>ind offers a uniquely integrated experience where an AI assistant acts like a life coach—delivering smart, reflective prompts based on each user's emotional tone and behavioural patterns. EverKind is the only platform that seamlessly blends therapy, coaching, and journaling into one cohesive ecosystem."
    },
    {
      question: "Why would users love <PERSON><PERSON><PERSON>?",
      answer: "Help is always just one click away. No waiting rooms. No friction. Just immediate, meaningful support."
    },
    {
      question: "How is EverKind different?",
      answer: "EverKind integrates greater emotional depth with structured insight to offer the most comprehensive support experience. EverKind regularly tests anonymized real conversations against other LLM's to ensure our models are differentiated and comprehensive."
    }
  ]

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle email submission here
    console.log("Email submitted:", email)
    alert("Thank you for joining our waiting list!")
    setEmail("")
  }

  return (
    <div className="min-h-screen flex flex-col">
      {/* Coming Soon Banner */}
      <div className="bg-[#F4D03F] text-center py-2">
        <span className="text-black font-semibold text-sm tracking-wide">COMING SOON</span>
      </div>

      {/* Header */}
      <header className="bg-[#8d56db] py-16 px-6 relative overflow-hidden">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left side - Text and Email Input */}
            <div className="space-y-8">
              {/* Logo and Tagline */}
              <div className="space-y-4">
                <div className="flex items-center">
                  <Image
                    src="/Landinglogo.svg"
                    alt="EverKind"
                    width={200}
                    height={55}
                    className="h-8 sm:h-10 md:h-12 lg:h-14 w-auto"
                  />
                </div>
              </div>

              <div className="space-y-4">
                <p className="text-lg text-white/90 leading-relaxed max-w-md">
                  EverKind combines cutting-edge AI technology with evidence-based wellness practices to help you achieve balance, clarity, and personal growth.
                </p>
              </div>

              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-white tracking-wide">
                  JOIN OUR WAITING LIST
                </h3>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="flex">
                    <Input
                      type="email"
                      placeholder="Enter your Email Address"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      className="flex-1 h-12 bg-white border-0 text-gray-900 placeholder:text-gray-500 focus:ring-0 focus:border-0 rounded-r-none"
                    />
                    <Button
                      type="submit"
                      className="bg-[#4ECDC4] hover:bg-[#45B7B8] text-white font-semibold px-6 h-12 rounded-l-none transition-colors border-0"
                    >
                      ▶
                    </Button>
                  </div>

                  <div className="flex items-start space-x-3 max-w-sm">
                    <Checkbox
                      id="subscription-agreement"
                      checked={subscriptionAgreed}
                      onCheckedChange={(checked) => setSubscriptionAgreed(checked === true)}
                      className="mt-1 bg-white border-white data-[state=checked]:bg-white data-[state=checked]:text-[#8d56db] flex-shrink-0"
                    />
                    <label
                      htmlFor="subscription-agreement"
                      className="text-xs text-white/90 leading-relaxed cursor-pointer"
                    >
                      Yes, I would like to receive email according to my subscription preferences. I understand I can unsubscribe at any time.
                    </label>
                  </div>
                </form>
              </div>
            </div>

            {/* Right side - Hero Image */}
            <div className="relative">
              <div className="relative">
                {/* Organic shape background */}
                <div className="absolute inset-0 bg-gradient-to-br from-[#4ECDC4] to-[#45B7B8] rounded-[3rem] transform rotate-12 scale-110"></div>
                <div className="relative bg-white rounded-[2rem] p-8 transform -rotate-6">
                  <Image
                    src="/hero-image.png"
                    alt="Person in hoodie with modern building background"
                    width={400}
                    height={400}
                    className="w-full h-auto rounded-xl"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Middle Section - FAQs */}
      <main className="flex-1 py-16 px-6 bg-gray-50">
        <div className="max-w-4xl mx-auto">
          <div className="text-left mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-[#8d56db] mb-4">
              FAQs
            </h2>
          </div>

          <div className="space-y-8">
            {faqs.map((faq, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-sm">
                <h3 className="font-semibold text-gray-900 text-lg mb-3">
                  {faq.question}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {faq.answer}
                </p>
              </div>
            ))}
          </div>
        </div>
      </main>

      {/* Footer */}
      <Footer />
    </div>
  )
}
