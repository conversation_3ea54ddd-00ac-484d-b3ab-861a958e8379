import Image from "next/image"

export default function Footer() {
  return (
    <footer className="bg-[#8d56db] py-12 px-6 relative overflow-hidden">
      <div className="max-w-6xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
          {/* Left side - Logo and Copyright */}
          <div className="space-y-4">
            <div className="flex items-center">
              <Image
                src="/Landinglogo.svg"
                alt="EverKind"
                width={200}
                height={55}
                className="h-6 sm:h-8 md:h-10 lg:h-12 w-auto"
              />
            </div>
            <p className="text-white/60 text-sm">
              © {new Date().getFullYear()} EverKind. All rights reserved.
            </p>
          </div>

          {/* Right side - Decorative SVG elements */}
          <div className="flex justify-center md:justify-end relative">
            {/* Small dark purple hexagon - positioned at top right area */}
            <div className="absolute -top-16 right-4 transform translate-x-8">
              <Image
                src="/95.svg"
                alt=""
                width={80}
                height={88}
                className="w-20 h-auto md:w-24 lg:w-28"
              />
            </div>
            {/* Large teal hexagon outline - positioned at top extending beyond right edge */}
            <div className="absolute -top-20 -right-16 transform translate-x-24 -translate-y-4 z-10">
              <Image
                src="/96.svg"
                alt=""
                width={200}
                height={220}
                className="w-48 h-auto md:w-56 lg:w-64"
              />
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
