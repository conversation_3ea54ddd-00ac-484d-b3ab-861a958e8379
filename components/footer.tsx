import Image from "next/image"

export default function Footer() {
  return (
    <footer className="bg-[#8d56db] py-12 px-6 relative overflow-hidden">
      <div className="max-w-6xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
          {/* Left side - Logo and Copyright */}
          <div className="space-y-4">
            <div className="flex items-center">
              <Image
                src="/Landinglogo.svg"
                alt="EverKind"
                width={200}
                height={55}
                className="h-6 sm:h-8 md:h-10 lg:h-12 w-auto"
              />
            </div>
            <p className="text-white/60 text-sm">
              © {new Date().getFullYear()} EverKind. All rights reserved.
            </p>
          </div>

          {/* Right side - Decorative SVG elements */}
          <div className="flex justify-center md:justify-end relative">
            {/* Large teal hexagon outline positioned on the right */}
            <div className="absolute top-0 right-0 transform translate-x-8 -translate-y-4">
              <Image
                src="/96.svg"
                alt=""
                width={120}
                height={132}
                className="w-24 h-auto md:w-28 lg:w-32 opacity-90"
              />
            </div>
            
            {/* Small dark purple hexagon positioned lower and to the left */}
            <div className="absolute bottom-0 right-16 transform translate-y-2 -translate-x-4">
              <Image
                src="/95.svg"
                alt=""
                width={55}
                height={61}
                className="w-12 h-auto md:w-14 lg:w-16 opacity-95"
              />
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
