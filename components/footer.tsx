import Image from "next/image"

export default function Footer() {
  return (
    <footer className="bg-[#8d56db] py-12 px-6 relative overflow-hidden">
      <div className="max-w-6xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
          {/* Left side - Logo and Copyright */}
          <div className="space-y-4">
            <div className="flex items-center">
              <Image
                src="/Landinglogo.svg"
                alt="EverKind"
                width={200}
                height={55}
                className="h-6 sm:h-8 md:h-10 lg:h-12 w-auto"
              />
            </div>
            <p className="text-white/60 text-sm">
              © {new Date().getFullYear()} EverKind. All rights reserved.
            </p>
          </div>

          {/* Right side - Decorative SVG elements */}
          <div className="flex justify-center md:justify-end relative h-24">
            {/* Small dark purple hexagon positioned on the upper right */}
            <div className="absolute -top-6 right-4 transform translate-x-2">
              <Image
                src="/95.svg"
                alt=""
                width={55}
                height={61}
                className="w-20 h-auto md:w-24 lg:w-28"
              />
            </div>

            {/* Large teal hexagon outline positioned lower and extending beyond */}
            <div className="absolute -top-2 -right-4 transform translate-x-16 translate-y-8">
              <Image
                src="/96.svg"
                alt=""
                width={120}
                height={132}
                className="w-36 h-auto md:w-40 lg:w-44"
              />
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
